# 多网站项目 - Moevend Website

这是一个多网站管理项目，包含萌贩机公司网站、Graphock 产品官网等多个独立网站，使用统一的部署和管理架构。

## 项目概述

本项目采用现代化的前端技术栈和多域名架构，支持：

- 多个独立网站的统一管理
- Nginx 反向代理和负载均衡
- PM2 进程管理
- 自动化部署和监控

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **样式框架**: TailwindCSS + TailwindUI
- **路由管理**: Vue Router
- **状态管理**: Pinia
- **反向代理**: Nginx
- **进程管理**: PM2
- **包管理器**: npm

## 网站列表

### 1. 萌贩机网站 (moevend.cn)

- **端口**: 3001
- **描述**: 杭州虹色萌机科技有限公司官方网站
- **功能**: 公司介绍、服务展示、联系方式

### 2. Graphock 网站 (graphock.cn)

- **端口**: 3002
- **描述**: 图数据可视化分析软件产品官网
- **功能**: 产品特性、在线演示、技术文档

### 3. 示例网站模板

- **端口**: 3003
- **描述**: 新网站开发模板
- **功能**: 快速创建新网站的基础模板

## 快速开始

### 开发环境

```bash
# 克隆项目
git clone <repository-url>
cd moevend-website

# 启动Moevend网站开发服务器
cd sites/moevend
npm install
npm run dev

# 启动Graphock网站开发服务器 (新终端)
cd sites/graphock
npm install
npm run dev
```

### 生产环境部署

详细部署说明请参考 [DEPLOYMENT.md](./DEPLOYMENT.md)

```bash
# 服务器初始化
sudo ./scripts/server-init.sh

# 部署所有网站
./scripts/deploy.sh

# 管理网站服务
./scripts/site-manager.sh status
```

## 项目结构

```
moevend-website/
├── sites/                    # 网站目录
│   ├── moevend/              # 萌贩机网站
│   ├── graphock/             # Graphock网站
│   └── example-site/         # 示例网站模板
├── nginx/                    # Nginx配置
├── pm2/                      # PM2配置
├── scripts/                  # 部署脚本
├── logs/                     # 日志目录
├── DEPLOYMENT.md             # 部署指南
└── README.md                 # 项目说明
```

## 开发指南

### 添加新网站

1. 复制示例模板
2. 修改配置文件
3. 分配新端口
4. 更新 Nginx 和 PM2 配置
5. 部署上线

详细步骤请参考 [DEPLOYMENT.md](./DEPLOYMENT.md#添加新网站)

### 代码规范

- 使用 TypeScript 进行类型检查
- 遵循 Vue 3 Composition API 规范
- 使用 TailwindCSS 进行样式开发
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case

## 常用命令

### 站点管理

```bash
# 查看所有站点状态
./scripts/site-manager.sh status

# 启动/停止/重启站点
./scripts/site-manager.sh start moevend
./scripts/site-manager.sh stop graphock
./scripts/site-manager.sh restart all

# 查看日志
./scripts/site-manager.sh logs moevend

# 构建站点
./scripts/site-manager.sh build all
```

### 开发调试

```bash
# 开发模式启动
./scripts/site-manager.sh dev moevend

# 查看实时日志
pm2 logs moevend-website --lines 100

# 监控系统资源
pm2 monit
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证

## 联系我们

- **公司**: 杭州虹色萌机科技有限公司
- **网站**: [moevend.com](https://moevend.com)
- **邮箱**: <<EMAIL>>

## 更新日志

### v1.0.0 (2025-01-08)

- 初始版本发布
- 完成 Moevend 和 Graphock 网站开发
- 实现多网站部署架构
- 添加自动化部署脚本
