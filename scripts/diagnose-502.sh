#!/bin/bash

# 502错误诊断脚本
# 用于快速诊断和修复Nginx 502 Bad Gateway错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# 检查端口监听
check_port() {
    local port=$1
    local service_name=$2

    if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
        print_success "$service_name 正在监听端口 $port"
        return 0
    else
        print_error "$service_name 没有监听端口 $port"
        return 1
    fi
}

# 查找Node.js应用实际运行的端口
find_nodejs_port() {
    local site_name=${1:-"moevend"}
    local ports=$(netstat -tlnp 2>/dev/null | grep node | awk -F: '{print $2}' | awk '{print $1}' | sort -u)

    for port in $ports; do
        if curl -s -I "http://localhost:$port" >/dev/null 2>&1; then
            echo $port
            return 0
        fi
    done

    # 检查常见的Vite端口 (根据网站类型)
    local common_ports
    if [ "$site_name" = "moevend" ]; then
        common_ports="3001 4173 3000 5173"
    elif [ "$site_name" = "graphock" ]; then
        common_ports="3002 4174 3000 5173"
    else
        common_ports="3001 3002 4173 4174 3000 5173"
    fi

    for port in $common_ports; do
        if netstat -tlnp 2>/dev/null | grep -q ":$port " && curl -s -I "http://localhost:$port" >/dev/null 2>&1; then
            echo $port
            return 0
        fi
    done

    return 1
}

# 查找所有运行的网站端口
find_all_ports() {
    local found_ports=""

    # 检查所有可能的端口
    for port in 3001 3002 4173 4174 3000 5173; do
        if netstat -tlnp 2>/dev/null | grep -q ":$port " && curl -s -I "http://localhost:$port" >/dev/null 2>&1; then
            found_ports="$found_ports $port"
        fi
    done

    echo $found_ports
}

# 更新Nginx配置中的端口
update_nginx_port() {
    local new_port=$1
    local config_file="/etc/nginx/sites-available/moevend.conf"

    if [ -f "$config_file" ]; then
        print_info "更新Nginx配置，将端口改为 $new_port"
        sudo sed -i "s/proxy_pass http:\/\/.*:.*\;/proxy_pass http:\/\/localhost:$new_port;/g" "$config_file"
        sudo sed -i "s/proxy_pass http:\/\/127\.0\.0\.1:.*\;/proxy_pass http:\/\/localhost:$new_port;/g" "$config_file"

        # 测试配置
        if sudo nginx -t >/dev/null 2>&1; then
            print_success "Nginx配置更新成功"
            sudo systemctl reload nginx
            return 0
        else
            print_error "Nginx配置更新失败"
            return 1
        fi
    else
        print_error "Nginx配置文件不存在: $config_file"
        return 1
    fi
}

# 检查服务状态
check_service() {
    local service=$1
    
    if systemctl is-active --quiet $service; then
        print_success "$service 服务正在运行"
        return 0
    else
        print_error "$service 服务未运行"
        return 1
    fi
}

# 测试HTTP响应
test_http() {
    local url=$1
    local expected_code=$2
    
    local response=$(curl -s -I "$url" 2>/dev/null | head -n1 | awk '{print $2}')
    
    if [ "$response" = "$expected_code" ]; then
        print_success "$url 响应正常 ($response)"
        return 0
    else
        print_error "$url 响应异常 (期望: $expected_code, 实际: $response)"
        return 1
    fi
}

# 主诊断函数
main() {
    print_info "开始502错误诊断..."
    
    # 步骤1: 检查Nginx状态
    print_step "步骤1: 检查Nginx服务状态"
    if check_service nginx; then
        if check_port 80 "Nginx HTTP" && check_port 443 "Nginx HTTPS"; then
            print_success "Nginx服务正常"
        else
            print_warning "Nginx服务运行但端口监听异常"
        fi
    else
        print_error "Nginx服务未运行，尝试启动..."
        sudo systemctl start nginx
        sleep 2
        check_service nginx
    fi
    
    # 步骤2: 检查Node.js应用状态
    print_step "步骤2: 检查Node.js应用状态"

    # 检查所有运行的端口
    local all_ports=$(find_all_ports)

    if [ -n "$all_ports" ]; then
        print_success "发现运行中的应用端口: $all_ports"

        # 检查moevend网站 (端口3001或其他)
        local moevend_port=$(find_nodejs_port "moevend")
        if [ -n "$moevend_port" ]; then
            print_success "Moevend网站运行在端口 $moevend_port"
        else
            print_warning "Moevend网站未运行"
        fi

        # 检查graphock网站 (端口3002或其他)
        local graphock_port=$(find_nodejs_port "graphock")
        if [ -n "$graphock_port" ]; then
            print_success "Graphock网站运行在端口 $graphock_port"
        else
            print_warning "Graphock网站未运行"
        fi

        # 使用第一个找到的端口作为主要端口
        local actual_port=$(echo $all_ports | awk '{print $1}')
        print_info "使用端口 $actual_port 进行主要测试"

        # 测试本地访问
        if test_http "http://localhost:$actual_port" "200"; then
            print_success "Node.js应用响应正常"

            # 检查Nginx配置是否指向正确端口
            if grep -q "proxy_pass.*:$actual_port" /etc/nginx/sites-available/moevend.conf 2>/dev/null; then
                print_success "Nginx配置端口正确"
            else
                print_warning "Nginx配置端口不匹配，正在更新..."
                update_nginx_port $actual_port
            fi
        else
            print_error "Node.js应用无法正常响应"
        fi
    else
        # 检查常见端口
        local found_port=""
        for port in 3001 4173 3000 5173; do
            if check_port $port "Node.js应用" >/dev/null 2>&1; then
                found_port=$port
                break
            fi
        done

        if [ -n "$found_port" ]; then
            print_success "发现Node.js应用在端口 $found_port"
            if test_http "http://localhost:$found_port" "200"; then
                print_success "Node.js应用响应正常"
                update_nginx_port $found_port
            fi
        else
            print_error "Node.js应用未运行"
            print_info "尝试启动Node.js应用..."

            # 检查项目目录
            if [ -d "/var/www/moevend-website/sites/moevend" ]; then
                cd /var/www/moevend-website/sites/moevend

                # 停止可能存在的进程
                pkill -f "vite preview" 2>/dev/null || true
                pkill -f "npm.*preview" 2>/dev/null || true

                # 检查dist目录
                if [ -d "dist" ]; then
                    print_info "找到dist目录，启动应用..."

                    # 确保vite可用
                    if ! command -v vite >/dev/null 2>&1; then
                        print_info "安装vite..."
                        npm install
                    fi

                    # 启动应用并获取端口
                    npm run preview > /tmp/vite_output.log 2>&1 &
                    local vite_pid=$!

                    # 等待启动并检测端口
                    sleep 8

                    # 从日志中提取端口号
                    local detected_port=$(grep -o "http://localhost:[0-9]*" /tmp/vite_output.log 2>/dev/null | head -1 | grep -o "[0-9]*$")

                    if [ -n "$detected_port" ]; then
                        print_success "应用启动在端口 $detected_port"

                        # 测试应用
                        if test_http "http://localhost:$detected_port" "200"; then
                            print_success "应用响应正常"
                            update_nginx_port $detected_port
                        else
                            print_error "应用启动但无法响应"
                        fi
                    else
                        print_error "无法检测到应用端口"
                        print_info "查看启动日志:"
                        cat /tmp/vite_output.log
                    fi
                else
                    print_warning "未找到dist目录，需要构建项目"
                    print_info "执行构建..."
                    npm install
                    npm run build

                    if [ -d "dist" ]; then
                        print_info "构建完成，启动应用..."
                        npm run preview > /tmp/vite_output.log 2>&1 &
                        sleep 8

                        local detected_port=$(grep -o "http://localhost:[0-9]*" /tmp/vite_output.log 2>/dev/null | head -1 | grep -o "[0-9]*$")
                        if [ -n "$detected_port" ]; then
                            print_success "应用启动在端口 $detected_port"
                            update_nginx_port $detected_port
                        fi
                    else
                        print_error "构建失败"
                    fi
                fi
            else
                print_error "项目目录不存在: /var/www/moevend-website/sites/moevend"
            fi
        fi
    fi
    
    # 步骤3: 检查PM2状态
    print_step "步骤3: 检查PM2进程管理"
    if command -v pm2 >/dev/null 2>&1; then
        print_info "PM2进程状态:"
        pm2 status
        
        # 检查是否有moevend相关进程
        if pm2 list | grep -q "moevend"; then
            print_success "发现PM2管理的moevend进程"
        else
            print_warning "未发现PM2管理的moevend进程"
            print_info "建议使用PM2管理应用"
        fi
    else
        print_warning "PM2未安装"
    fi
    
    # 步骤4: 测试网站访问
    print_step "步骤4: 测试网站访问"

    # 查找实际运行的端口
    local running_port=$(find_nodejs_port)

    if [ -n "$running_port" ]; then
        print_info "检测到应用运行在端口 $running_port"

        # 测试本地访问
        if test_http "http://localhost:$running_port" "200"; then
            print_success "本地Node.js应用访问正常"

            # 检查Nginx配置是否正确
            if grep -q "proxy_pass.*:$running_port" /etc/nginx/sites-available/moevend.conf 2>/dev/null; then
                print_success "Nginx配置端口正确"

                # 测试HTTPS访问
                print_info "测试HTTPS访问..."
                local https_response=$(curl -s -I "https://moevend.cn" 2>/dev/null | head -n1 | awk '{print $2}')

                if [ "$https_response" = "200" ]; then
                    print_success "HTTPS访问正常"
                elif [ "$https_response" = "502" ]; then
                    print_error "仍然是502错误，可能是防火墙或其他网络问题"
                else
                    print_warning "HTTPS访问异常，响应码: $https_response"
                fi
            else
                print_error "Nginx配置端口不匹配，应用在端口 $running_port 但Nginx配置指向其他端口"
                print_info "正在修复Nginx配置..."
                update_nginx_port $running_port

                # 重新测试
                sleep 3
                local https_response=$(curl -s -I "https://moevend.cn" 2>/dev/null | head -n1 | awk '{print $2}')
                if [ "$https_response" = "200" ]; then
                    print_success "修复后HTTPS访问正常"
                else
                    print_error "修复后仍有问题，响应码: $https_response"
                fi
            fi
        else
            print_error "本地Node.js应用无法正常响应"
        fi
    else
        print_error "未找到运行中的Node.js应用，这是502错误的根本原因"

        # 尝试检查常见端口
        print_info "检查常见端口..."
        for port in 3001 4173 3000 5173; do
            if netstat -tlnp 2>/dev/null | grep -q ":$port "; then
                print_info "发现端口 $port 有进程监听"
                if test_http "http://localhost:$port" "200"; then
                    print_success "端口 $port 响应正常"
                    update_nginx_port $port
                    break
                fi
            fi
        done
    fi
    
    # 步骤5: 检查日志
    print_step "步骤5: 检查错误日志"
    
    print_info "最近的Nginx错误日志:"
    if [ -f "/var/log/nginx/error.log" ]; then
        sudo tail -n 10 /var/log/nginx/error.log
    else
        print_warning "Nginx错误日志文件不存在"
    fi
    
    print_info "最近的Nginx访问日志:"
    if [ -f "/var/log/nginx/access.log" ]; then
        sudo tail -n 5 /var/log/nginx/access.log
    else
        print_warning "Nginx访问日志文件不存在"
    fi
    
    # 步骤6: 提供修复建议
    print_step "步骤6: 修复建议"

    local app_port=$(find_nodejs_port)

    if [ -z "$app_port" ]; then
        print_info "Node.js应用未运行，修复建议:"
        echo "1. 进入项目目录: cd /var/www/moevend-website/sites/moevend"
        echo "2. 构建项目: npm run build"
        echo "3. 启动应用: npm run preview"
        echo "4. 或使用PM2管理: pm2 start npm --name 'moevend-website' -- run preview"
        echo "5. 注意：Vite预览服务器默认使用端口4173，不是3001"
    elif ! test_http "http://localhost:$app_port" "200" >/dev/null 2>&1; then
        print_info "Node.js应用运行在端口 $app_port 但响应异常，建议:"
        echo "1. 检查应用日志: cat /tmp/vite_output.log"
        echo "2. 重启应用: pkill -f 'vite preview' && npm run preview"
        echo "3. 检查依赖是否完整: npm install"
    else
        # 检查Nginx配置是否匹配
        if grep -q "proxy_pass.*:$app_port" /etc/nginx/sites-available/moevend.conf 2>/dev/null; then
            print_info "Node.js应用和Nginx配置都正常，可能的问题:"
            echo "1. 防火墙阻止了内部连接"
            echo "2. 域名解析问题"
            echo "3. SSL证书问题"
            echo "4. 检查Nginx错误日志: sudo tail -f /var/log/nginx/error.log"
        else
            print_info "端口配置不匹配问题:"
            echo "1. 应用运行在端口: $app_port"
            echo "2. 但Nginx配置指向其他端口"
            echo "3. 运行自动修复: ./scripts/diagnose-502.sh --fix"
            echo "4. 或手动更新Nginx配置文件"
        fi
    fi

    print_info "快速修复命令:"
    echo "./scripts/diagnose-502.sh --fix"
    
    print_info "诊断完成！"
}

# 显示帮助信息
show_help() {
    echo "502错误诊断脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help           显示此帮助信息"
    echo "  --fix            尝试自动修复常见问题"
    echo ""
    echo "示例:"
    echo "  $0               # 运行诊断"
    echo "  $0 --fix         # 诊断并尝试修复"
}

# 自动修复函数
auto_fix() {
    print_info "尝试自动修复502错误..."

    # 停止现有进程
    print_info "停止现有进程..."
    pm2 stop all 2>/dev/null || true
    pm2 delete all 2>/dev/null || true
    pkill -f "vite preview" 2>/dev/null || true
    pkill -f "npm.*preview" 2>/dev/null || true

    # 进入项目目录
    if [ ! -d "/var/www/moevend-website/sites/moevend" ]; then
        print_error "项目目录不存在: /var/www/moevend-website/sites/moevend"
        return 1
    fi

    cd /var/www/moevend-website/sites/moevend

    # 构建项目
    print_info "构建项目..."
    npm install
    npm run build

    if [ ! -d "dist" ]; then
        print_error "构建失败，dist目录不存在"
        return 1
    fi

    # 启动应用 (使用配置的端口3001)
    print_info "启动应用..."

    # 使用--port参数强制使用3001端口，避免Vite使用默认4173
    npm run preview -- --port 3001 --host > /tmp/vite_output.log 2>&1 &
    local vite_pid=$!

    # 等待启动
    sleep 10

    # 检查进程是否还在运行
    if kill -0 $vite_pid 2>/dev/null; then
        print_success "Vite进程启动成功 (PID: $vite_pid)"
    else
        print_error "Vite进程启动失败"
        print_info "查看启动日志:"
        cat /tmp/vite_output.log
        return 1
    fi

    # 检测实际端口
    local detected_port=$(find_nodejs_port "moevend")

    if [ -n "$detected_port" ]; then
        print_success "应用启动在端口 $detected_port"

        # 测试应用响应
        if test_http "http://localhost:$detected_port" "200"; then
            print_success "应用响应正常"

            # 更新Nginx配置
            print_info "更新Nginx配置..."
            update_nginx_port $detected_port

            # 重启Nginx
            print_info "重启Nginx..."
            sudo systemctl restart nginx

            # 等待Nginx启动
            sleep 5

            # 最终测试
            print_info "测试修复结果..."
            local final_response=$(curl -s -I "https://moevend.cn" 2>/dev/null | head -n1 | awk '{print $2}')

            if [ "$final_response" = "200" ]; then
                print_success "修复成功！网站现在可以正常访问"
                print_info "应用运行在端口: $detected_port"
                print_info "网站地址: https://moevend.cn"
            elif [ "$final_response" = "403" ]; then
                print_error "403错误：这是Vite主机保护机制"
                print_info "Vite配置已更新，请重新构建并启动："
                echo "cd /var/www/moevend-website/sites/moevend"
                echo "npm run build"
                echo "npm run preview -- --port 3001 --host"
            else
                print_error "修复失败，响应码: $final_response"
                print_info "查看Nginx错误日志:"
                sudo tail -n 10 /var/log/nginx/error.log
            fi
        else
            print_error "应用启动但无法响应"
            print_info "查看启动日志:"
            cat /tmp/vite_output.log
        fi
    else
        print_error "无法检测到应用端口"
        print_info "查看启动日志:"
        cat /tmp/vite_output.log

        # 尝试手动检测端口
        print_info "尝试手动检测端口..."
        local found_port=$(find_nodejs_port)
        if [ -n "$found_port" ]; then
            print_success "发现应用在端口 $found_port"
            update_nginx_port $found_port
            sudo systemctl restart nginx
            sleep 5
            test_http "https://moevend.cn" "200"
        fi
    fi
}

# 解析参数
case "${1:-}" in
    --help)
        show_help
        exit 0
        ;;
    --fix)
        auto_fix
        ;;
    *)
        main
        ;;
esac
