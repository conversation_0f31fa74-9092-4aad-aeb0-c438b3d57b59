#!/bin/bash

# 部署脚本
# 用于构建和部署所有网站

set -e

PROJECT_ROOT="/var/www/moevend-website"
BACKUP_DIR="/var/backups/moevend-website"

echo "开始部署流程..."

# 创建备份
echo "创建备份..."
if [ -d "$PROJECT_ROOT" ]; then
    sudo mkdir -p "$BACKUP_DIR"
    sudo cp -r "$PROJECT_ROOT" "$BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S)"
fi

# 进入项目目录
cd "$PROJECT_ROOT"

# 拉取最新代码
echo "拉取最新代码..."
git pull origin main

# 安装依赖并构建所有网站
echo "构建 Moevend 网站..."
cd sites/moevend
npm install
npm run build
cd ../..

echo "构建 Graphock 网站..."
cd sites/graphock
npm install
npm run build
cd ../..

# 复制 Nginx 配置
echo "更新 Nginx 配置..."
sudo cp nginx/sites-available/*.conf /etc/nginx/sites-available/
sudo ln -sf /etc/nginx/sites-available/moevend.conf /etc/nginx/sites-enabled/
sudo ln -sf /etc/nginx/sites-available/graphock.conf /etc/nginx/sites-enabled/

# 测试 Nginx 配置
echo "测试 Nginx 配置..."
sudo nginx -t

# 重新加载 Nginx
echo "重新加载 Nginx..."
sudo systemctl reload nginx

# 重启 PM2 应用
echo "重启 PM2 应用..."
pm2 stop all || true
pm2 start pm2/ecosystem.config.js --env production
pm2 save

echo "部署完成！"
echo "请检查以下服务状态："
echo "- Nginx: sudo systemctl status nginx"
echo "- PM2: pm2 status"
echo "- 网站访问: curl -I http://localhost:3001"
