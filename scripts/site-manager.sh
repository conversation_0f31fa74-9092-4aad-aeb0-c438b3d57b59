#!/bin/bash

# 站点管理脚本
# 用于管理多个网站的启动、停止、重启等操作

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 显示帮助信息
show_help() {
    echo "站点管理脚本"
    echo ""
    echo "用法: $0 [命令] [站点名称]"
    echo ""
    echo "命令:"
    echo "  start [site]     启动指定站点或所有站点"
    echo "  stop [site]      停止指定站点或所有站点"
    echo "  restart [site]   重启指定站点或所有站点"
    echo "  status           显示所有站点状态"
    echo "  logs [site]      显示指定站点日志"
    echo "  build [site]     构建指定站点或所有站点"
    echo "  dev [site]       以开发模式启动站点"
    echo "  list             列出所有可用站点"
    echo ""
    echo "站点名称:"
    echo "  moevend          萌贩机网站"
    echo "  graphock         Graphock网站"
    echo "  all              所有站点"
}

# 列出所有站点
list_sites() {
    echo "可用站点:"
    echo "- moevend (端口: 3001)"
    echo "- graphock (端口: 3002)"
}

# 启动站点
start_site() {
    local site=$1
    
    if [ "$site" = "all" ] || [ -z "$site" ]; then
        echo "启动所有站点..."
        pm2 start "$PROJECT_ROOT/pm2/ecosystem.config.js" --env production
    else
        echo "启动站点: $site"
        pm2 start "$PROJECT_ROOT/pm2/ecosystem.config.js" --only "${site}-website" --env production
    fi
}

# 停止站点
stop_site() {
    local site=$1
    
    if [ "$site" = "all" ] || [ -z "$site" ]; then
        echo "停止所有站点..."
        pm2 stop all
    else
        echo "停止站点: $site"
        pm2 stop "${site}-website"
    fi
}

# 重启站点
restart_site() {
    local site=$1
    
    if [ "$site" = "all" ] || [ -z "$site" ]; then
        echo "重启所有站点..."
        pm2 restart all
    else
        echo "重启站点: $site"
        pm2 restart "${site}-website"
    fi
}

# 显示状态
show_status() {
    echo "PM2 进程状态:"
    pm2 status
    echo ""
    echo "Nginx 状态:"
    sudo systemctl status nginx --no-pager -l
}

# 显示日志
show_logs() {
    local site=$1
    
    if [ -z "$site" ]; then
        pm2 logs
    else
        pm2 logs "${site}-website"
    fi
}

# 构建站点
build_site() {
    local site=$1
    
    if [ "$site" = "all" ] || [ -z "$site" ]; then
        echo "构建所有站点..."
        cd "$PROJECT_ROOT/sites/moevend" && npm run build
        cd "$PROJECT_ROOT/sites/graphock" && npm run build
    else
        echo "构建站点: $site"
        cd "$PROJECT_ROOT/sites/$site" && npm run build
    fi
}

# 开发模式
dev_mode() {
    local site=$1
    
    if [ -z "$site" ]; then
        echo "开发模式需要指定站点名称"
        exit 1
    fi
    
    echo "以开发模式启动站点: $site"
    pm2 start "$PROJECT_ROOT/pm2/ecosystem.dev.config.js" --only "${site}-dev"
}

# 主逻辑
case "$1" in
    start)
        start_site "$2"
        ;;
    stop)
        stop_site "$2"
        ;;
    restart)
        restart_site "$2"
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs "$2"
        ;;
    build)
        build_site "$2"
        ;;
    dev)
        dev_mode "$2"
        ;;
    list)
        list_sites
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        echo "未知命令: $1"
        echo "使用 '$0 help' 查看帮助信息"
        exit 1
        ;;
esac
