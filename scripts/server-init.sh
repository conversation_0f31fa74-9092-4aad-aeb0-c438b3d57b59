#!/bin/bash

# 服务器初始化脚本
# 用于在新服务器上安装和配置必要的软件

set -e

echo "开始服务器初始化..."

# 更新系统
echo "更新系统包..."
sudo apt update && sudo apt upgrade -y

# 安装基础软件
echo "安装基础软件..."
sudo apt install -y curl wget git vim htop unzip software-properties-common

# 安装 Node.js (使用 NodeSource 仓库)
echo "安装 Node.js..."
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs

# 验证 Node.js 安装
node_version=$(node --version)
npm_version=$(npm --version)
echo "Node.js 版本: $node_version"
echo "npm 版本: $npm_version"

# 安装 PM2
echo "安装 PM2..."
sudo npm install -g pm2

# 安装 Nginx
echo "安装 Nginx..."
sudo apt install -y nginx

# 启动并启用 Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# 配置防火墙
echo "配置防火墙..."
sudo ufw allow OpenSSH
sudo ufw allow 'Nginx Full'
sudo ufw --force enable

# 创建部署用户
echo "创建部署用户..."
if ! id "deploy" &>/dev/null; then
    sudo adduser --disabled-password --gecos "" deploy
    sudo usermod -aG sudo deploy
    sudo mkdir -p /home/<USER>/.ssh
    sudo chown deploy:deploy /home/<USER>/.ssh
    sudo chmod 700 /home/<USER>/.ssh
fi

# 创建项目目录
echo "创建项目目录..."
sudo mkdir -p /var/www/moevend-website
sudo chown deploy:deploy /var/www/moevend-website

# 创建日志目录
sudo mkdir -p /var/log/moevend-website
sudo chown deploy:deploy /var/log/moevend-website

# 设置 PM2 开机启动
echo "设置 PM2 开机启动..."
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u deploy --hp /home/<USER>

echo "服务器初始化完成！"
echo "请手动完成以下步骤："
echo "1. 配置 SSH 密钥"
echo "2. 配置 SSL 证书"
echo "3. 设置域名 DNS 解析"
echo "4. 运行部署脚本"
