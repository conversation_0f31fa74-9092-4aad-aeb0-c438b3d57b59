#!/bin/bash

# Vite主机保护修复脚本
# 解决 "This host is not allowed" 错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# 修复Vite配置
fix_vite_config() {
    local site_path=$1
    local domain=$2
    local port=$3
    
    print_info "修复 $site_path 的Vite配置..."
    
    if [ ! -f "$site_path/vite.config.ts" ]; then
        print_error "配置文件不存在: $site_path/vite.config.ts"
        return 1
    fi
    
    # 检查是否已有preview配置
    if grep -q "preview:" "$site_path/vite.config.ts"; then
        print_info "Preview配置已存在，跳过"
        return 0
    fi
    
    # 备份原配置
    cp "$site_path/vite.config.ts" "$site_path/vite.config.ts.backup"
    
    # 添加preview配置
    sed -i "/build: {/i\\
  preview: {\\
    port: $port,\\
    host: true,\\
    allowedHosts: ['$domain', 'www.$domain', 'localhost', '127.0.0.1']\\
  }," "$site_path/vite.config.ts"
    
    print_success "Vite配置已更新"
}

# 重新构建和启动应用
rebuild_and_start() {
    local site_path=$1
    local port=$2
    
    print_info "重新构建 $site_path..."
    
    cd "$site_path"
    
    # 停止现有进程
    pkill -f "vite preview.*$port" 2>/dev/null || true
    pkill -f "npm.*preview" 2>/dev/null || true
    
    # 重新构建
    npm run build
    
    if [ ! -d "dist" ]; then
        print_error "构建失败，dist目录不存在"
        return 1
    fi
    
    # 启动应用，强制使用指定端口和主机
    print_info "启动应用在端口 $port..."
    npm run preview -- --port $port --host 0.0.0.0 > "/tmp/vite_${port}.log" 2>&1 &
    
    # 等待启动
    sleep 8
    
    # 检查是否启动成功
    if curl -s -I "http://localhost:$port" >/dev/null 2>&1; then
        print_success "应用启动成功，端口 $port"
        return 0
    else
        print_error "应用启动失败"
        print_info "查看日志:"
        cat "/tmp/vite_${port}.log"
        return 1
    fi
}

# 更新Nginx配置
update_nginx_config() {
    local domain=$1
    local port=$2
    local config_file="/etc/nginx/sites-available/${domain}.conf"
    
    if [ ! -f "$config_file" ]; then
        print_error "Nginx配置文件不存在: $config_file"
        return 1
    fi
    
    print_info "更新Nginx配置，端口 $port..."
    
    # 更新proxy_pass端口
    sudo sed -i "s/proxy_pass http:\/\/.*:[0-9]*;/proxy_pass http:\/\/localhost:$port;/g" "$config_file"
    
    # 测试配置
    if sudo nginx -t >/dev/null 2>&1; then
        print_success "Nginx配置更新成功"
        sudo systemctl reload nginx
        return 0
    else
        print_error "Nginx配置测试失败"
        return 1
    fi
}

# 主修复函数
main() {
    print_info "开始修复Vite主机保护问题..."
    
    # 检查项目目录
    if [ ! -d "/var/www/moevend-website" ]; then
        print_error "项目目录不存在: /var/www/moevend-website"
        exit 1
    fi
    
    cd /var/www/moevend-website
    
    # 修复Moevend网站
    print_step "修复Moevend网站"
    
    if [ -d "sites/moevend" ]; then
        fix_vite_config "sites/moevend" "moevend.cn" 3001
        
        if rebuild_and_start "sites/moevend" 3001; then
            update_nginx_config "moevend" 3001
            
            # 测试访问
            sleep 5
            local response=$(curl -s -I "https://moevend.cn" 2>/dev/null | head -n1 | awk '{print $2}')
            
            if [ "$response" = "200" ]; then
                print_success "Moevend网站修复成功！"
                print_info "访问地址: https://moevend.cn"
            else
                print_error "Moevend网站仍有问题，响应码: $response"
            fi
        fi
    else
        print_warning "Moevend网站目录不存在"
    fi
    
    # 修复Graphock网站 (如果存在)
    print_step "修复Graphock网站"
    
    if [ -d "sites/graphock" ]; then
        fix_vite_config "sites/graphock" "graphock.cn" 3002
        
        if rebuild_and_start "sites/graphock" 3002; then
            # 检查是否有graphock的SSL证书
            if [ -f "/etc/letsencrypt/live/graphock.cn/fullchain.pem" ]; then
                update_nginx_config "graphock" 3002
                
                # 测试访问
                sleep 5
                local response=$(curl -s -I "https://graphock.cn" 2>/dev/null | head -n1 | awk '{print $2}')
                
                if [ "$response" = "200" ]; then
                    print_success "Graphock网站修复成功！"
                    print_info "访问地址: https://graphock.cn"
                else
                    print_error "Graphock网站仍有问题，响应码: $response"
                fi
            else
                print_warning "Graphock网站没有SSL证书，跳过Nginx配置"
                print_info "如需配置Graphock网站，请先申请SSL证书:"
                echo "sudo certbot --nginx -d graphock.cn -d www.graphock.cn"
            fi
        fi
    else
        print_warning "Graphock网站目录不存在"
    fi
    
    print_info "修复完成！"
    
    # 显示运行状态
    print_step "当前运行状态"
    
    print_info "运行中的端口:"
    netstat -tlnp 2>/dev/null | grep ":300[12] " || echo "无"
    
    print_info "PM2进程 (如果使用):"
    pm2 status 2>/dev/null || echo "PM2未运行"
    
    print_info "建议使用PM2管理应用:"
    echo "pm2 start npm --name 'moevend-website' --cwd /var/www/moevend-website/sites/moevend -- run preview -- --port 3001 --host 0.0.0.0"
    echo "pm2 start npm --name 'graphock-website' --cwd /var/www/moevend-website/sites/graphock -- run preview -- --port 3002 --host 0.0.0.0"
    echo "pm2 save"
    echo "pm2 startup"
}

# 显示帮助
show_help() {
    echo "Vite主机保护修复脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help           显示此帮助信息"
    echo "  --moevend-only   仅修复Moevend网站"
    echo "  --graphock-only  仅修复Graphock网站"
    echo ""
    echo "功能:"
    echo "  - 修复Vite配置，添加allowedHosts"
    echo "  - 重新构建应用"
    echo "  - 使用正确的端口和主机启动"
    echo "  - 更新Nginx配置"
    echo "  - 验证网站访问"
}

# 解析参数
case "${1:-}" in
    --help)
        show_help
        exit 0
        ;;
    --moevend-only)
        print_info "仅修复Moevend网站..."
        # 这里可以添加仅修复Moevend的逻辑
        ;;
    --graphock-only)
        print_info "仅修复Graphock网站..."
        # 这里可以添加仅修复Graphock的逻辑
        ;;
    *)
        main
        ;;
esac
