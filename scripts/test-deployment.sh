#!/bin/bash

# 部署测试脚本
# 用于验证网站部署是否成功

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "\n${BLUE}=== $1 ===${NC}"
}

# 测试HTTP响应
test_response() {
    local url=$1
    local expected_code=$2
    local description=$3
    
    print_info "测试 $description: $url"
    
    local response=$(curl -s -I "$url" 2>/dev/null | head -n1 | awk '{print $2}')
    
    if [ "$response" = "$expected_code" ]; then
        print_success "$description 测试通过 (响应码: $response)"
        return 0
    else
        print_error "$description 测试失败 (期望: $expected_code, 实际: $response)"
        return 1
    fi
}

# 主测试函数
main() {
    print_info "开始部署验证测试..."
    
    # 测试1: 检查服务状态
    print_step "测试1: 检查服务状态"
    
    if systemctl is-active --quiet nginx; then
        print_success "Nginx 服务运行正常"
    else
        print_error "Nginx 服务未运行"
        return 1
    fi
    
    # 查找Node.js应用端口
    local app_ports=$(netstat -tlnp 2>/dev/null | grep node | awk -F: '{print $2}' | awk '{print $1}' | sort -u)
    local found_port=""
    
    for port in $app_ports 4173 3001 3000 5173; do
        if netstat -tlnp 2>/dev/null | grep -q ":$port " && curl -s -I "http://localhost:$port" >/dev/null 2>&1; then
            found_port=$port
            break
        fi
    done
    
    if [ -n "$found_port" ]; then
        print_success "Node.js 应用运行在端口 $found_port"
    else
        print_error "未找到运行中的 Node.js 应用"
        return 1
    fi
    
    # 测试2: 本地访问测试
    print_step "测试2: 本地访问测试"
    
    if test_response "http://localhost:$found_port" "200" "本地应用访问"; then
        print_success "本地应用响应正常"
    else
        print_error "本地应用无法访问"
        return 1
    fi
    
    # 测试3: HTTP重定向测试
    print_step "测试3: HTTP重定向测试"
    
    if test_response "http://moevend.cn" "301" "HTTP重定向"; then
        print_success "HTTP到HTTPS重定向正常"
    else
        print_error "HTTP重定向失败"
    fi
    
    # 测试4: HTTPS访问测试
    print_step "测试4: HTTPS访问测试"
    
    if test_response "https://moevend.cn" "200" "HTTPS访问"; then
        print_success "HTTPS访问正常"
        print_success "🎉 网站部署成功！"
        
        print_info "网站信息:"
        echo "  - 网站地址: https://moevend.cn"
        echo "  - 应用端口: $found_port"
        echo "  - SSL证书: Let's Encrypt"
        echo "  - 服务状态: 正常运行"
        
    else
        print_error "HTTPS访问失败"
        
        # 提供诊断建议
        print_info "诊断建议:"
        echo "1. 运行诊断脚本: ./scripts/diagnose-502.sh"
        echo "2. 检查Nginx配置: sudo nginx -t"
        echo "3. 查看错误日志: sudo tail -f /var/log/nginx/error.log"
        echo "4. 尝试自动修复: ./scripts/diagnose-502.sh --fix"
        
        return 1
    fi
    
    # 测试5: SSL证书验证
    print_step "测试5: SSL证书验证"
    
    local ssl_info=$(echo | openssl s_client -connect moevend.cn:443 -servername moevend.cn 2>/dev/null | openssl x509 -noout -dates 2>/dev/null)
    
    if [ -n "$ssl_info" ]; then
        print_success "SSL证书验证通过"
        echo "$ssl_info"
    else
        print_error "SSL证书验证失败"
    fi
    
    # 测试6: 性能测试
    print_step "测试6: 响应时间测试"
    
    local response_time=$(curl -o /dev/null -s -w "%{time_total}" "https://moevend.cn")
    
    if (( $(echo "$response_time < 3.0" | bc -l) )); then
        print_success "响应时间良好: ${response_time}秒"
    else
        print_error "响应时间较慢: ${response_time}秒"
    fi
    
    print_info "部署验证完成！"
}

# 显示帮助信息
show_help() {
    echo "部署测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --help           显示此帮助信息"
    echo "  --quick          快速测试 (仅测试基本功能)"
    echo ""
    echo "示例:"
    echo "  $0               # 完整测试"
    echo "  $0 --quick       # 快速测试"
}

# 快速测试
quick_test() {
    print_info "快速测试模式..."
    
    # 检查HTTPS访问
    if test_response "https://moevend.cn" "200" "HTTPS访问"; then
        print_success "✅ 网站运行正常"
        print_info "网站地址: https://moevend.cn"
    else
        print_error "❌ 网站访问失败"
        print_info "运行完整诊断: ./scripts/diagnose-502.sh"
    fi
}

# 解析参数
case "${1:-}" in
    --help)
        show_help
        exit 0
        ;;
    --quick)
        quick_test
        ;;
    *)
        main
        ;;
esac
