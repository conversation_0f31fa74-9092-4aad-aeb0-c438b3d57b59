<template>
  <section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
          体验Graphock的强大功能
        </h2>
        <p class="mt-4 text-lg text-gray-600">
          通过在线演示，感受图数据可视化的魅力
        </p>
      </div>

      <div class="mt-16">
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
          <!-- Demo Preview -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-6">
              <h3 class="text-xl font-semibold text-gray-900 mb-4">社交网络分析</h3>
              <div class="bg-gradient-to-br from-blue-100 to-purple-100 rounded-lg h-64 flex items-center justify-center">
                <div class="text-center">
                  <div class="text-4xl mb-2">🌐</div>
                  <p class="text-gray-600">交互式图形演示</p>
                </div>
              </div>
              <p class="mt-4 text-gray-600">
                探索社交网络中的用户关系，识别影响者和社区结构。
              </p>
            </div>
          </div>

          <!-- Features List -->
          <div class="space-y-6">
            <div class="bg-white rounded-lg shadow-lg p-6">
              <h3 class="text-xl font-semibold text-gray-900 mb-4">演示功能</h3>
              <ul class="space-y-3">
                <li class="flex items-start">
                  <span class="text-primary-600 mr-3 mt-1">✓</span>
                  <div>
                    <h4 class="font-medium text-gray-900">动态布局</h4>
                    <p class="text-gray-600 text-sm">多种图布局算法，实时调整节点位置</p>
                  </div>
                </li>
                <li class="flex items-start">
                  <span class="text-primary-600 mr-3 mt-1">✓</span>
                  <div>
                    <h4 class="font-medium text-gray-900">节点筛选</h4>
                    <p class="text-gray-600 text-sm">根据属性和关系筛选显示节点</p>
                  </div>
                </li>
                <li class="flex items-start">
                  <span class="text-primary-600 mr-3 mt-1">✓</span>
                  <div>
                    <h4 class="font-medium text-gray-900">路径分析</h4>
                    <p class="text-gray-600 text-sm">查找节点间的最短路径和关键路径</p>
                  </div>
                </li>
                <li class="flex items-start">
                  <span class="text-primary-600 mr-3 mt-1">✓</span>
                  <div>
                    <h4 class="font-medium text-gray-900">社区检测</h4>
                    <p class="text-gray-600 text-sm">自动识别图中的社区和群组</p>
                  </div>
                </li>
              </ul>
            </div>

            <div class="text-center">
              <RouterLink
                to="/demo"
                class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors"
              >
                立即体验演示
                <svg class="ml-2 -mr-1 w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </RouterLink>
            </div>
          </div>
        </div>
      </div>

      <!-- Statistics -->
      <div class="mt-16 bg-white rounded-lg shadow-lg p-8">
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          <div class="text-center">
            <div class="text-3xl font-bold text-primary-600">10K+</div>
            <div class="text-gray-600">节点处理能力</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-primary-600">100K+</div>
            <div class="text-gray-600">边关系分析</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-primary-600">50+</div>
            <div class="text-gray-600">分析算法</div>
          </div>
          <div class="text-center">
            <div class="text-3xl font-bold text-primary-600">99.9%</div>
            <div class="text-gray-600">系统稳定性</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
</script>
