<template>
  <footer class="bg-gray-900 text-white">
    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
        <!-- Company Info -->
        <div class="col-span-1 md:col-span-2">
          <h3 class="text-2xl font-bold text-primary-400 mb-4">Graphock</h3>
          <p class="text-gray-300 mb-4">
            专业的图数据可视化分析软件，让复杂的数据关系变得清晰可见。通过直观的图形界面和强大的分析功能，帮助用户发现数据中的隐藏模式和洞察。
          </p>
          <p class="text-gray-400 text-sm">
            让数据分析更简单，让洞察更深刻
          </p>
        </div>

        <!-- Quick Links -->
        <div>
          <h4 class="text-lg font-semibold mb-4">快速链接</h4>
          <ul class="space-y-2">
            <li>
              <RouterLink to="/" class="text-gray-300 hover:text-primary-400 transition-colors">
                首页
              </RouterLink>
            </li>
            <li>
              <RouterLink to="/features" class="text-gray-300 hover:text-primary-400 transition-colors">
                产品特性
              </RouterLink>
            </li>
            <li>
              <RouterLink to="/demo" class="text-gray-300 hover:text-primary-400 transition-colors">
                在线演示
              </RouterLink>
            </li>
            <li>
              <RouterLink to="/about" class="text-gray-300 hover:text-primary-400 transition-colors">
                关于我们
              </RouterLink>
            </li>
            <li>
              <RouterLink to="/contact" class="text-gray-300 hover:text-primary-400 transition-colors">
                联系我们
              </RouterLink>
            </li>
          </ul>
        </div>

        <!-- Features -->
        <div>
          <h4 class="text-lg font-semibold mb-4">核心功能</h4>
          <ul class="space-y-2 text-gray-300">
            <li>图数据可视化</li>
            <li>关系分析</li>
            <li>模式识别</li>
            <li>交互式探索</li>
            <li>数据导入导出</li>
          </ul>
        </div>
      </div>

      <!-- Bottom Bar -->
      <div class="mt-8 pt-8 border-t border-gray-700">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <p class="text-gray-400 text-sm">
            © 2025 Graphock. 保留所有权利.
          </p>
          <div class="mt-4 md:mt-0">
            <p class="text-gray-400 text-sm">
              专业图数据分析工具
            </p>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
</script>
