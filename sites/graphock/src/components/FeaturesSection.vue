<template>
  <section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h2 class="text-3xl font-extrabold text-gray-900 sm:text-4xl">
          强大的功能特性
        </h2>
        <p class="mt-4 text-lg text-gray-600">
          专为图数据分析而设计的全面功能集
        </p>
      </div>

      <div class="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
        <!-- 图数据可视化 -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
          <div class="p-6">
            <div class="flex items-center justify-center h-16 w-16 rounded-md bg-primary-500 text-white mx-auto mb-4">
              <span class="text-2xl">📊</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 text-center mb-2">
              图数据可视化
            </h3>
            <p class="text-gray-600 text-center">
              将复杂的图数据转换为直观的可视化图形，支持多种布局算法和自定义样式。
            </p>
          </div>
        </div>

        <!-- 关系分析 -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
          <div class="p-6">
            <div class="flex items-center justify-center h-16 w-16 rounded-md bg-primary-500 text-white mx-auto mb-4">
              <span class="text-2xl">🔗</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 text-center mb-2">
              关系分析
            </h3>
            <p class="text-gray-600 text-center">
              深入分析节点间的关系，发现隐藏的连接模式和影响路径。
            </p>
          </div>
        </div>

        <!-- 模式识别 -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
          <div class="p-6">
            <div class="flex items-center justify-center h-16 w-16 rounded-md bg-primary-500 text-white mx-auto mb-4">
              <span class="text-2xl">🎯</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 text-center mb-2">
              模式识别
            </h3>
            <p class="text-gray-600 text-center">
              自动识别图中的重要模式和结构，如社区、中心节点和异常连接。
            </p>
          </div>
        </div>

        <!-- 交互式探索 -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
          <div class="p-6">
            <div class="flex items-center justify-center h-16 w-16 rounded-md bg-primary-500 text-white mx-auto mb-4">
              <span class="text-2xl">🖱️</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 text-center mb-2">
              交互式探索
            </h3>
            <p class="text-gray-600 text-center">
              通过拖拽、缩放、筛选等交互方式，自由探索数据的不同维度。
            </p>
          </div>
        </div>

        <!-- 数据导入导出 -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
          <div class="p-6">
            <div class="flex items-center justify-center h-16 w-16 rounded-md bg-primary-500 text-white mx-auto mb-4">
              <span class="text-2xl">📁</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 text-center mb-2">
              数据导入导出
            </h3>
            <p class="text-gray-600 text-center">
              支持多种数据格式的导入导出，包括CSV、JSON、GraphML等。
            </p>
          </div>
        </div>

        <!-- 实时分析 -->
        <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
          <div class="p-6">
            <div class="flex items-center justify-center h-16 w-16 rounded-md bg-primary-500 text-white mx-auto mb-4">
              <span class="text-2xl">⚡</span>
            </div>
            <h3 class="text-xl font-semibold text-gray-900 text-center mb-2">
              实时分析
            </h3>
            <p class="text-gray-600 text-center">
              高性能的实时数据处理和分析能力，支持大规模图数据。
            </p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
