<template>
  <footer class="bg-gradient-to-br from-gray-900 to-gray-800 text-white">
    <div class="max-w-7xl container-responsive py-20 px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-12 md:gap-16">
        <!-- Company Info -->
        <div class="col-span-1 md:col-span-2">
          <h3 class="text-3xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent mb-6">
            萌贩机
          </h3>
          <p class="text-gray-300 mb-6 text-lg leading-relaxed">
            杭州虹色萌机科技有限公司专注于潮玩商品设计和销售、日用百货零售、软件开发和销售、移动端app开发和销售、无人售货机开发和销售。
          </p>
          <p class="text-gray-400">
            公司名称：杭州虹色萌机科技有限公司
          </p>
        </div>

        <!-- Quick Links -->
        <div>
          <h4 class="text-xl font-bold mb-6">快速链接</h4>
          <ul class="space-y-3">
            <li>
              <RouterLink to="/" class="text-gray-300 hover:text-primary-400 transition-all duration-300 text-lg hover:translate-x-1 transform inline-block">
                首页
              </RouterLink>
            </li>
            <li>
              <RouterLink to="/about" class="text-gray-300 hover:text-primary-400 transition-all duration-300 text-lg hover:translate-x-1 transform inline-block">
                关于我们
              </RouterLink>
            </li>
            <li>
              <RouterLink to="/services" class="text-gray-300 hover:text-primary-400 transition-all duration-300 text-lg hover:translate-x-1 transform inline-block">
                服务项目
              </RouterLink>
            </li>
            <li>
              <RouterLink to="/contact" class="text-gray-300 hover:text-primary-400 transition-all duration-300 text-lg hover:translate-x-1 transform inline-block">
                联系我们
              </RouterLink>
            </li>
          </ul>
        </div>

        <!-- Services -->
        <div>
          <h4 class="text-xl font-bold mb-6">主营业务</h4>
          <ul class="space-y-3 text-gray-300">
            <li class="flex items-center text-lg">
              <span class="w-2 h-2 bg-primary-400 rounded-full mr-3"></span>
              潮玩商品设计和销售
            </li>
            <li class="flex items-center text-lg">
              <span class="w-2 h-2 bg-secondary-400 rounded-full mr-3"></span>
              日用百货零售
            </li>
            <li class="flex items-center text-lg">
              <span class="w-2 h-2 bg-accent-400 rounded-full mr-3"></span>
              软件开发和销售
            </li>
            <li class="flex items-center text-lg">
              <span class="w-2 h-2 bg-primary-400 rounded-full mr-3"></span>
              移动端App开发
            </li>
            <li class="flex items-center text-lg">
              <span class="w-2 h-2 bg-secondary-400 rounded-full mr-3"></span>
              无人售货机开发
            </li>
          </ul>
        </div>
      </div>

      <!-- Bottom Bar -->
      <div class="mt-16 pt-12 border-t border-gray-700/50">
        <div class="flex flex-col md:flex-row justify-between items-center">
          <p class="text-gray-400 text-lg">
            © 2025 杭州虹色萌机科技有限公司. 保留所有权利.
          </p>
          <div class="mt-4 md:mt-0">
            <p class="text-gray-400 text-lg font-medium">
              让科技更有趣，让生活更美好
            </p>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
</script>
