<template>
  <!-- Hero Section -->
  <section class="relative bg-gradient-to-br from-primary-50 via-white to-secondary-50 overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-gradient-to-br from-primary-50/80 via-transparent to-secondary-50/80"></div>
    
    <!-- 动态背景元素 -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute top-1/4 left-1/4 w-72 h-72 bg-primary-200/20 rounded-full blur-3xl animate-pulse-slow"></div>
      <div class="absolute top-3/4 right-1/4 w-96 h-96 bg-secondary-200/20 rounded-full blur-3xl animate-pulse-slow" style="animation-delay: 1s;"></div>
      <div class="absolute bottom-1/4 left-1/3 w-80 h-80 bg-accent-200/20 rounded-full blur-3xl animate-pulse-slow" style="animation-delay: 2s;"></div>
    </div>

    <div class="relative max-w-7xl container-responsive px-4 sm:px-6 lg:px-8 py-28 lg:py-36">
      <div class="text-center">
        <!-- 主标题 -->
        <h1 class="text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold text-gray-900 leading-tight">
          <span class="block bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent mb-4">
            杭州虹色萌机科技
          </span>
          <span class="block text-gray-800 text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-medium">
            让科技更有趣，让生活更美好
          </span>
        </h1>

        <!-- 副标题 -->
        <p class="mt-12 text-xl sm:text-2xl md:text-3xl text-gray-600 max-w-5xl mx-auto leading-relaxed">
          专注于潮玩商品设计、软件开发、移动应用和无人售货机等多元化业务
          <br class="hidden sm:block" />
          致力于为用户提供创新的科技产品和服务
        </p>

        <!-- CTA按钮 -->
        <div class="mt-16 flex flex-col sm:flex-row gap-8 justify-center items-center">
          <RouterLink
            to="/services"
            class="group relative inline-flex items-center justify-center px-8 md:px-10 py-4 md:py-5 text-base md:text-lg font-semibold rounded-2xl text-white bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:-translate-y-2 overflow-hidden w-full sm:w-auto"
          >
            <span class="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
            <span class="relative">了解我们的服务</span>
            <svg class="ml-3 w-5 h-5 md:w-6 md:h-6 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </RouterLink>
          <RouterLink
            to="/contact"
            class="group inline-flex items-center justify-center px-8 md:px-10 py-4 md:py-5 text-base md:text-lg font-semibold rounded-2xl text-primary-700 bg-white/80 backdrop-blur-sm border-2 border-primary-600 hover:bg-primary-50 transition-all duration-300 shadow-xl hover:shadow-2xl transform hover:-translate-y-1 w-full sm:w-auto"
          >
            <span class="relative">联系我们</span>
            <svg class="ml-3 w-5 h-5 md:w-6 md:h-6 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
          </RouterLink>
        </div>
      </div>

      <!-- 业务图标展示 -->
      <div class="mt-28 md:mt-32">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 md:gap-12 max-w-6xl mx-auto">
          <div class="text-center group">
            <div class="relative">
              <div class="w-24 h-24 mx-auto bg-gradient-to-br from-primary-500 to-primary-600 rounded-3xl flex items-center justify-center text-4xl text-white shadow-2xl group-hover:scale-110 transition-all duration-500 animate-float">
                🎨
              </div>
              <div class="absolute inset-0 w-24 h-24 mx-auto bg-gradient-to-br from-primary-400 to-primary-500 rounded-3xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500"></div>
            </div>
            <p class="mt-6 text-lg font-semibold text-gray-800">潮玩设计</p>
            <p class="mt-2 text-sm text-gray-600">创意无限，设计精美</p>
          </div>
          <div class="text-center group">
            <div class="relative">
              <div class="w-24 h-24 mx-auto bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-3xl flex items-center justify-center text-4xl text-white shadow-2xl group-hover:scale-110 transition-all duration-500 animate-float" style="animation-delay: 0.5s;">
                💻
              </div>
              <div class="absolute inset-0 w-24 h-24 mx-auto bg-gradient-to-br from-secondary-400 to-secondary-500 rounded-3xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500"></div>
            </div>
            <p class="mt-6 text-lg font-semibold text-gray-800">软件开发</p>
            <p class="mt-2 text-sm text-gray-600">技术领先，质量保证</p>
          </div>
          <div class="text-center group">
            <div class="relative">
              <div class="w-24 h-24 mx-auto bg-gradient-to-br from-accent-500 to-accent-600 rounded-3xl flex items-center justify-center text-4xl text-white shadow-2xl group-hover:scale-110 transition-all duration-500 animate-float" style="animation-delay: 1s;">
                📱
              </div>
              <div class="absolute inset-0 w-24 h-24 mx-auto bg-gradient-to-br from-accent-400 to-accent-500 rounded-3xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500"></div>
            </div>
            <p class="mt-6 text-lg font-semibold text-gray-800">移动应用</p>
            <p class="mt-2 text-sm text-gray-600">用户体验，移动优先</p>
          </div>
          <div class="text-center group">
            <div class="relative">
              <div class="w-24 h-24 mx-auto bg-gradient-to-br from-green-500 to-green-600 rounded-3xl flex items-center justify-center text-4xl text-white shadow-2xl group-hover:scale-110 transition-all duration-500 animate-float" style="animation-delay: 1.5s;">
                🤖
              </div>
              <div class="absolute inset-0 w-24 h-24 mx-auto bg-gradient-to-br from-green-400 to-green-500 rounded-3xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500"></div>
            </div>
            <p class="mt-6 text-lg font-semibold text-gray-800">智能设备</p>
            <p class="mt-2 text-sm text-gray-600">智能化，自动化</p>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
</script>
