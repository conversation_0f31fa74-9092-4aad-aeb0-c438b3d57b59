<template>
  <section class="py-32 bg-white">
    <div class="max-w-7xl container-responsive px-4 sm:px-6 lg:px-8">
      <div class="text-center">
        <h2 class="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
          <span class="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
            为什么选择我们
          </span>
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
          专业团队，创新技术，优质服务
        </p>
        <div class="mt-8 flex justify-center">
          <div class="w-32 h-1 bg-gradient-to-r from-primary-600 to-secondary-600 rounded-full"></div>
        </div>
      </div>

      <div class="mt-24">
        <div class="grid grid-cols-1 gap-10 sm:grid-cols-2 lg:grid-cols-4 md:gap-12 lg:gap-16">
          <!-- 创新设计 -->
          <div class="text-center group">
            <div class="relative">
              <div class="flex items-center justify-center h-24 w-24 rounded-3xl bg-gradient-to-br from-primary-500 to-primary-600 mx-auto mb-6 shadow-2xl group-hover:scale-110 transition-all duration-500">
                <span class="text-4xl">✨</span>
              </div>
              <div class="absolute inset-0 flex items-center justify-center h-24 w-24 mx-auto bg-gradient-to-br from-primary-400 to-primary-500 rounded-3xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500"></div>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">创新设计</h3>
            <p class="text-gray-600 leading-relaxed">
              紧跟潮流趋势，打造独特的产品设计理念
            </p>
          </div>

          <!-- 技术领先 -->
          <div class="text-center group">
            <div class="relative">
              <div class="flex items-center justify-center h-24 w-24 rounded-3xl bg-gradient-to-br from-secondary-500 to-secondary-600 mx-auto mb-6 shadow-2xl group-hover:scale-110 transition-all duration-500">
                <span class="text-4xl">🚀</span>
              </div>
              <div class="absolute inset-0 flex items-center justify-center h-24 w-24 mx-auto bg-gradient-to-br from-secondary-400 to-secondary-500 rounded-3xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500"></div>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">技术领先</h3>
            <p class="text-gray-600 leading-relaxed">
              采用最新技术栈，确保产品的先进性和稳定性
            </p>
          </div>

          <!-- 品质保证 -->
          <div class="text-center group">
            <div class="relative">
              <div class="flex items-center justify-center h-24 w-24 rounded-3xl bg-gradient-to-br from-accent-500 to-accent-600 mx-auto mb-6 shadow-2xl group-hover:scale-110 transition-all duration-500">
                <span class="text-4xl">🏆</span>
              </div>
              <div class="absolute inset-0 flex items-center justify-center h-24 w-24 mx-auto bg-gradient-to-br from-accent-400 to-accent-500 rounded-3xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500"></div>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">品质保证</h3>
            <p class="text-gray-600 leading-relaxed">
              严格的质量控制体系，确保每一个产品的高品质
            </p>
          </div>

          <!-- 贴心服务 -->
          <div class="text-center group">
            <div class="relative">
              <div class="flex items-center justify-center h-24 w-24 rounded-3xl bg-gradient-to-br from-green-500 to-green-600 mx-auto mb-6 shadow-2xl group-hover:scale-110 transition-all duration-500">
                <span class="text-4xl">💝</span>
              </div>
              <div class="absolute inset-0 flex items-center justify-center h-24 w-24 mx-auto bg-gradient-to-br from-green-400 to-green-500 rounded-3xl blur-xl opacity-50 group-hover:opacity-75 transition-opacity duration-500"></div>
            </div>
            <h3 class="text-xl font-bold text-gray-900 mb-3">贴心服务</h3>
            <p class="text-gray-600 leading-relaxed">
              专业的客户服务团队，提供全方位的售后支持
            </p>
          </div>
        </div>
      </div>

      <!-- CTA Section -->
      <div class="mt-28 text-center">
        <div class="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-3xl shadow-2xl p-12 border border-primary-100">
          <h3 class="text-3xl font-bold text-gray-900 mb-6">
            准备开始您的项目了吗？
          </h3>
          <p class="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
            联系我们，让我们一起创造美好的未来
          </p>
          <RouterLink
            to="/contact"
            class="group inline-flex items-center px-10 py-4 text-lg font-semibold rounded-2xl text-white bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:-translate-y-1"
          >
            立即联系
            <svg class="ml-3 w-6 h-6 transform group-hover:translate-x-1 transition-transform" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </RouterLink>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
</script>
