<template>
  <!-- Services Section -->
  <section class="py-32 bg-gradient-to-br from-gray-50 to-primary-50/30">
    <div class="max-w-7xl container-responsive px-4 sm:px-6 lg:px-8">
      <!-- 标题区域 -->
      <div class="text-center mb-24">
        <h2 class="text-4xl sm:text-5xl font-bold text-gray-900 mb-6">
          <span class="bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
            我们的核心业务
          </span>
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
          多元化的业务布局，为客户提供全方位的科技解决方案
        </p>
        <div class="mt-8 flex justify-center">
          <div class="w-32 h-1 bg-gradient-to-r from-primary-600 to-secondary-600 rounded-full"></div>
        </div>
      </div>

      <!-- 服务网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 md:gap-10 lg:gap-12">
        <!-- 潮玩商品设计 -->
        <div
          class="group bg-white/80 backdrop-blur-sm rounded-2xl p-6 md:p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-100/50 hover:border-primary-200 transform hover:-translate-y-2"
        >
          <div class="flex items-center mb-6">
            <div
              class="w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center text-white text-xl md:text-2xl shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0"
            >
              🎨
            </div>
            <h3 class="ml-4 text-lg md:text-xl font-bold text-gray-900">潮玩商品设计</h3>
          </div>
          <p class="text-gray-600 leading-relaxed mb-6">
            创意设计各类潮流玩具和装饰品，为年轻消费者提供个性化的生活用品，引领时尚潮流。
          </p>
          <div class="flex items-center text-primary-600 font-semibold group-hover:text-primary-700 transition-colors">
            了解更多
            <svg class="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </div>
        </div>

        <!-- 日用百货零售 -->
        <div
          class="group bg-white/80 backdrop-blur-sm rounded-2xl p-6 md:p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-100/50 hover:border-secondary-200 transform hover:-translate-y-2"
        >
          <div class="flex items-center mb-6">
            <div
              class="w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center text-white text-xl md:text-2xl shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0"
            >
              🛍️
            </div>
            <h3 class="ml-4 text-lg md:text-xl font-bold text-gray-900">日用百货零售</h3>
          </div>
          <p class="text-gray-600 leading-relaxed mb-6">
            精选优质日用百货商品，为消费者提供便捷的购物体验和优质的生活用品。
          </p>
          <div class="flex items-center text-secondary-600 font-semibold group-hover:text-secondary-700 transition-colors">
            了解更多
            <svg class="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </div>
        </div>

        <!-- 软件开发 -->
        <div
          class="group bg-white/80 backdrop-blur-sm rounded-2xl p-6 md:p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-100/50 hover:border-accent-200 transform hover:-translate-y-2"
        >
          <div class="flex items-center mb-6">
            <div
              class="w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center text-white text-xl md:text-2xl shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0"
            >
              💻
            </div>
            <h3 class="ml-4 text-lg md:text-xl font-bold text-gray-900">软件开发</h3>
          </div>
          <p class="text-gray-600 leading-relaxed mb-6">
            专业的软件开发团队，提供定制化软件解决方案，满足企业数字化转型需求。
          </p>
          <div class="flex items-center text-accent-600 font-semibold group-hover:text-accent-700 transition-colors">
            了解更多
            <svg class="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </div>
        </div>

        <!-- 移动应用开发 -->
        <div
          class="group bg-white/80 backdrop-blur-sm rounded-2xl p-6 md:p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-100/50 hover:border-primary-200 transform hover:-translate-y-2"
        >
          <div class="flex items-center mb-6">
            <div
              class="w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center text-white text-xl md:text-2xl shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0"
            >
              📱
            </div>
            <h3 class="ml-4 text-lg md:text-xl font-bold text-gray-900">移动应用开发</h3>
          </div>
          <p class="text-gray-600 leading-relaxed mb-6">
            iOS和Android应用开发，为企业和个人用户打造优质的移动应用体验。
          </p>
          <div class="flex items-center text-primary-600 font-semibold group-hover:text-primary-700 transition-colors">
            了解更多
            <svg class="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </div>
        </div>

        <!-- 无人售货机 -->
        <div
          class="group bg-white/80 backdrop-blur-sm rounded-2xl p-6 md:p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-100/50 hover:border-secondary-200 transform hover:-translate-y-2"
        >
          <div class="flex items-center mb-6">
            <div
              class="w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center text-white text-xl md:text-2xl shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0"
            >
              🤖
            </div>
            <h3 class="ml-4 text-lg md:text-xl font-bold text-gray-900">智能售货设备</h3>
          </div>
          <p class="text-gray-600 leading-relaxed mb-6">
            智能无人售货机研发和销售，推动无人零售行业的创新发展。
          </p>
          <div class="flex items-center text-secondary-600 font-semibold group-hover:text-secondary-700 transition-colors">
            了解更多
            <svg class="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </div>
        </div>

        <!-- 无人零售 -->
        <div
          class="group bg-white/80 backdrop-blur-sm rounded-2xl p-6 md:p-8 shadow-xl hover:shadow-2xl transition-all duration-500 border border-gray-100/50 hover:border-accent-200 transform hover:-translate-y-2"
        >
          <div class="flex items-center mb-6">
            <div
              class="w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center text-white text-xl md:text-2xl shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0"
            >
              🏪
            </div>
            <h3 class="ml-4 text-lg md:text-xl font-bold text-gray-900">无人零售</h3>
          </div>
          <p class="text-gray-600 leading-relaxed mb-6">
            构建智能化无人零售生态系统，提供24小时便民服务。
          </p>
          <div class="flex items-center text-accent-600 font-semibold group-hover:text-accent-700 transition-colors">
            了解更多
            <svg class="ml-2 w-4 h-4 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </div>
        </div>
      </div>

      <!-- CTA区域 -->
      <div class="mt-28 text-center">
        <div class="bg-gradient-to-r from-primary-600 to-secondary-600 rounded-3xl p-12 shadow-2xl transform hover:-translate-y-1 transition-all duration-300">
          <h3 class="text-3xl font-bold text-white mb-4">想了解更多我们的服务？</h3>
          <p class="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            我们的专业团队随时为您提供咨询和服务，让我们一起创造美好的数字未来
          </p>
          <RouterLink
            to="/services"
            class="group inline-flex items-center px-10 py-4 text-lg font-semibold rounded-2xl text-primary-600 bg-white hover:bg-primary-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
          >
            查看详细服务
            <svg class="ml-3 w-6 h-6 transform group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </RouterLink>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
</script>
