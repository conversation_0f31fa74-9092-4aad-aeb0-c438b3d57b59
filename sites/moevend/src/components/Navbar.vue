<template>
  <nav class="bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100/50 sticky top-0 z-50">
    <div class="max-w-7xl container-responsive px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-20">
        <div class="flex items-center">
          <RouterLink to="/" class="flex items-center group">
            <div class="flex-shrink-0">
              <h1 class="text-2xl font-bold text-gray-900 group-hover:text-primary-600 transition-all duration-300">
                萌贩机科技
              </h1>
            </div>
          </RouterLink>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:flex items-center space-x-2">
          <RouterLink
            to="/"
            class="text-gray-700 hover:text-primary-600 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-primary-50 relative group"
            :class="{ 'text-primary-600 bg-primary-50 shadow-md': $route.path === '/' }"
          >
            首页
            <span class="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-primary-600 to-secondary-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
          </RouterLink>
          <RouterLink
            to="/about"
            class="text-gray-700 hover:text-primary-600 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-primary-50 relative group"
            :class="{ 'text-primary-600 bg-primary-50 shadow-md': $route.path === '/about' }"
          >
            关于我们
            <span class="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-primary-600 to-secondary-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
          </RouterLink>
          <RouterLink
            to="/services"
            class="text-gray-700 hover:text-primary-600 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-primary-50 relative group"
            :class="{ 'text-primary-600 bg-primary-50 shadow-md': $route.path === '/services' }"
          >
            服务项目
            <span class="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-primary-600 to-secondary-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
          </RouterLink>
          <RouterLink
            to="/contact"
            class="text-gray-700 hover:text-primary-600 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 hover:bg-primary-50 relative group"
            :class="{ 'text-primary-600 bg-primary-50 shadow-md': $route.path === '/contact' }"
          >
            联系我们
            <span class="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-primary-600 to-secondary-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></span>
          </RouterLink>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden flex items-center">
          <button
            @click="mobileMenuOpen = !mobileMenuOpen"
            class="text-gray-700 hover:text-primary-600 focus:outline-none focus:text-primary-600"
          >
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path
                v-if="!mobileMenuOpen"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              />
              <path
                v-else
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile Navigation -->
    <div v-show="mobileMenuOpen" class="md:hidden">
      <div class="max-w-7xl container-responsive px-4 sm:px-6 lg:px-8">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
        <RouterLink
          to="/"
          @click="mobileMenuOpen = false"
          class="block text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-base font-medium"
          :class="{ 'text-primary-600 bg-primary-50': $route.path === '/' }"
        >
          首页
        </RouterLink>
        <RouterLink
          to="/about"
          @click="mobileMenuOpen = false"
          class="block text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-base font-medium"
          :class="{ 'text-primary-600 bg-primary-50': $route.path === '/about' }"
        >
          关于我们
        </RouterLink>
        <RouterLink
          to="/services"
          @click="mobileMenuOpen = false"
          class="block text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-base font-medium"
          :class="{ 'text-primary-600 bg-primary-50': $route.path === '/services' }"
        >
          服务项目
        </RouterLink>
        <RouterLink
          to="/contact"
          @click="mobileMenuOpen = false"
          class="block text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-base font-medium"
          :class="{ 'text-primary-600 bg-primary-50': $route.path === '/contact' }"
        >
          联系我们
        </RouterLink>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { RouterLink } from 'vue-router'

const mobileMenuOpen = ref(false)
</script>
