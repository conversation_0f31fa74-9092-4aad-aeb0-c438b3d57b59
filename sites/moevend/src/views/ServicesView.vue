<template>
  <div class="services">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-primary-600 via-primary-700 to-primary-800 text-white py-16 md:py-20 overflow-hidden">
      <div class="absolute inset-0 bg-black/10"></div>
      <div class="relative max-w-7xl container-responsive px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 drop-shadow-lg">服务项目</h1>
        <p class="text-xl md:text-2xl opacity-95 max-w-4xl mx-auto leading-relaxed drop-shadow-md">
          全方位的科技解决方案，满足您的多样化需求
        </p>
      </div>
    </section>

    <!-- Services Grid -->
    <section class="py-24 bg-white">
      <div class="max-w-7xl container-responsive px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 md:gap-12 lg:gap-16">
          <!-- 潮玩商品设计和销售 -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-8">
              <div class="flex items-center mb-4">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white mr-4">
                  <span class="text-2xl">🎨</span>
                </div>
                <h3 class="text-2xl font-bold text-gray-900">潮玩商品设计和销售</h3>
              </div>
              <p class="text-gray-600 mb-4">
                我们专注于创意潮玩商品的设计和销售，为年轻消费者提供个性化、有趣的生活用品。
              </p>
              <ul class="text-gray-600 space-y-2">
                <li>• 原创IP设计开发</li>
                <li>• 潮流玩具定制</li>
                <li>• 装饰品设计制作</li>
                <li>• 周边商品开发</li>
              </ul>
            </div>
          </div>

          <!-- 日用百货零售 -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-8">
              <div class="flex items-center mb-4">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white mr-4">
                  <span class="text-2xl">🛍️</span>
                </div>
                <h3 class="text-2xl font-bold text-gray-900">日用百货零售</h3>
              </div>
              <p class="text-gray-600 mb-4">
                精选优质日用百货商品，为消费者提供便捷的购物体验和优质的生活用品。
              </p>
              <ul class="text-gray-600 space-y-2">
                <li>• 生活用品销售</li>
                <li>• 家居装饰用品</li>
                <li>• 个人护理产品</li>
                <li>• 便民服务商品</li>
              </ul>
            </div>
          </div>

          <!-- 软件开发和销售 -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-8">
              <div class="flex items-center mb-4">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white mr-4">
                  <span class="text-2xl">💻</span>
                </div>
                <h3 class="text-2xl font-bold text-gray-900">软件开发和销售</h3>
              </div>
              <p class="text-gray-600 mb-4">
                专业的软件开发团队，提供定制化软件解决方案，满足企业数字化转型需求。
              </p>
              <ul class="text-gray-600 space-y-2">
                <li>• 企业管理系统开发</li>
                <li>• 电商平台搭建</li>
                <li>• 数据分析系统</li>
                <li>• 定制化软件解决方案</li>
              </ul>
            </div>
          </div>

          <!-- 移动端App开发 -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-8">
              <div class="flex items-center mb-4">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white mr-4">
                  <span class="text-2xl">📱</span>
                </div>
                <h3 class="text-2xl font-bold text-gray-900">移动端App开发</h3>
              </div>
              <p class="text-gray-600 mb-4">
                iOS和Android应用开发，为企业和个人用户打造优质的移动应用体验。
              </p>
              <ul class="text-gray-600 space-y-2">
                <li>• iOS应用开发</li>
                <li>• Android应用开发</li>
                <li>• 跨平台应用开发</li>
                <li>• 应用维护和更新</li>
              </ul>
            </div>
          </div>

          <!-- 无人售货机开发 -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-8">
              <div class="flex items-center mb-4">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white mr-4">
                  <span class="text-2xl">🤖</span>
                </div>
                <h3 class="text-2xl font-bold text-gray-900">无人售货机开发</h3>
              </div>
              <p class="text-gray-600 mb-4">
                智能无人售货机研发和销售，推动无人零售行业的创新发展。
              </p>
              <ul class="text-gray-600 space-y-2">
                <li>• 智能售货机硬件开发</li>
                <li>• 支付系统集成</li>
                <li>• 远程监控管理</li>
                <li>• 数据分析和优化</li>
              </ul>
            </div>
          </div>

          <!-- 无人零售 -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-8">
              <div class="flex items-center mb-4">
                <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white mr-4">
                  <span class="text-2xl">🏪</span>
                </div>
                <h3 class="text-2xl font-bold text-gray-900">无人零售</h3>
              </div>
              <p class="text-gray-600 mb-4">
                构建智能化无人零售生态系统，提供24小时便民服务。
              </p>
              <ul class="text-gray-600 space-y-2">
                <li>• 无人便利店解决方案</li>
                <li>• 智能货架系统</li>
                <li>• 自助结算系统</li>
                <li>• 库存管理系统</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-24 bg-gray-50">
      <div class="max-w-7xl container-responsive px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">
          需要我们的服务？
        </h2>
        <p class="text-lg text-gray-600 mb-8">
          联系我们，获取专业的解决方案和报价
        </p>
        <RouterLink
          to="/contact"
          class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 transition-colors"
        >
          立即咨询
        </RouterLink>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { RouterLink } from 'vue-router'
</script>
