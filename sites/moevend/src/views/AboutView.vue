<template>
  <div class="about">
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-primary-600 via-primary-700 to-primary-800 text-white py-16 md:py-20 overflow-hidden">
      <div class="absolute inset-0 bg-black/10"></div>
      <div class="relative max-w-7xl container-responsive px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 drop-shadow-lg">关于我们</h1>
        <p class="text-xl md:text-2xl opacity-95 max-w-4xl mx-auto leading-relaxed drop-shadow-md">杭州虹色萌机科技有限公司 - 让科技更有趣，让生活更美好</p>
      </div>
    </section>

    <!-- Company Introduction -->
    <section class="py-24 bg-white">
      <div class="max-w-7xl container-responsive px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 md:gap-16 items-center">
          <div>
            <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-6">公司简介</h2>
            <p class="text-lg text-gray-600 mb-4">
              杭州虹色萌机科技有限公司成立于2024年，是一家专注于多元化科技业务的创新型企业。我们致力于将最新的科技成果转化为实用的产品和服务，为用户创造更加便捷、有趣的生活体验。
            </p>
            <p class="text-lg text-gray-600 mb-4">
              公司业务涵盖潮玩商品设计和销售、日用百货零售、软件开发和销售、移动端app开发和销售、无人售货机开发和销售等多个领域，形成了完整的产业链布局。
            </p>
            <p class="text-lg text-gray-600">
              我们拥有一支年轻而富有创造力的团队，始终坚持以用户需求为导向，以技术创新为驱动，为客户提供优质的产品和服务。
            </p>
          </div>
          <div class="bg-gradient-to-br from-primary-100 to-primary-200 rounded-lg p-8 text-center">
            <div class="text-6xl mb-4">🌈</div>
            <h3 class="text-2xl font-bold text-gray-900 mb-2">虹色萌机</h3>
            <p class="text-gray-600">科技创新 · 品质生活</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Mission & Vision -->
    <section class="py-24 bg-gray-50">
      <div class="max-w-7xl container-responsive px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-2xl md:text-3xl font-bold text-gray-900">愿景与使命</h2>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12">
          <div class="bg-white rounded-lg shadow-lg p-8 text-center">
            <div class="text-4xl mb-4">🎯</div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">我们的愿景</h3>
            <p class="text-gray-600">
              成为中国领先的多元化科技企业，通过持续创新和优质服务，为用户创造更加美好的生活体验，推动社会科技进步。
            </p>
          </div>
          <div class="bg-white rounded-lg shadow-lg p-8 text-center">
            <div class="text-4xl mb-4">💡</div>
            <h3 class="text-2xl font-bold text-gray-900 mb-4">我们的使命</h3>
            <p class="text-gray-600">
              致力于将前沿科技与日常生活相结合，开发创新产品和服务，让科技更加贴近用户需求，让每个人都能享受科技带来的便利和乐趣。
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Core Values -->
    <section class="py-24 bg-white">
      <div class="max-w-7xl container-responsive px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-2xl md:text-3xl font-bold text-gray-900">核心价值观</h2>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 md:gap-12">
          <div class="text-center p-6">
            <div class="text-3xl mb-3">🚀</div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">创新驱动</h3>
            <p class="text-gray-600 text-sm">持续创新，追求卓越</p>
          </div>
          <div class="text-center p-6">
            <div class="text-3xl mb-3">🤝</div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">用户至上</h3>
            <p class="text-gray-600 text-sm">以用户需求为中心</p>
          </div>
          <div class="text-center p-6">
            <div class="text-3xl mb-3">⭐</div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">品质保证</h3>
            <p class="text-gray-600 text-sm">严格把控产品质量</p>
          </div>
          <div class="text-center p-6">
            <div class="text-3xl mb-3">🌟</div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">团队协作</h3>
            <p class="text-gray-600 text-sm">携手共创美好未来</p>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

