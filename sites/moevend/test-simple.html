<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单居中测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1280px;
            margin: 0 auto;
            background: red;
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .container-responsive {
            max-width: 1280px;
            margin: 0 auto;
            background: blue;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        @media (min-width: 1920px) {
            .container-responsive {
                max-width: 1536px;
            }
        }
        
        @media (min-width: 2560px) {
            .container-responsive {
                max-width: 1792px;
            }
        }
        
        @media (min-width: 3840px) {
            .container-responsive {
                max-width: 2048px;
            }
        }
        
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: black;
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="debug-info">
        屏幕宽度: <span id="width"></span>px
    </div>
    
    <h1 style="text-align: center; margin-bottom: 30px;">居中测试页面</h1>
    
    <div class="container">
        固定1280px容器 (应该居中)
    </div>
    
    <div class="container-responsive">
        响应式容器 (应该居中且在大屏幕上更宽)
    </div>
    
    <script>
        function updateWidth() {
            document.getElementById('width').textContent = window.innerWidth;
        }
        updateWidth();
        window.addEventListener('resize', updateWidth);
    </script>
</body>
</html>