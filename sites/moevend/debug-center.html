<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>居中测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    screens: {
                        '2xl': '1536px',
                        '3xl': '1920px',
                        '4xl': '2560px',
                        '5xl': '3840px',
                    },
                    maxWidth: {
                        '8xl': '88rem',   // 1408px
                        '9xl': '96rem',   // 1536px
                        '10xl': '112rem', // 1792px
                        '11xl': '128rem', // 2048px
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-100">
    <div class="p-8">
        <h1 class="text-2xl font-bold mb-8 text-center">居中测试 - 不同屏幕尺寸</h1>
        
        <!-- 屏幕宽度显示 -->
        <div class="text-center mb-8">
            <p class="text-lg">当前屏幕宽度: <span id="screenWidth"></span>px</p>
        </div>
        
        <!-- 容器测试 -->
        <div class="space-y-8">
            <!-- max-w-7xl (1280px) -->
            <div class="bg-red-200 p-4">
                <div class="max-w-7xl mx-auto bg-blue-500 text-white p-4 text-center">
                    max-w-7xl (1280px) - 原始设置
                </div>
            </div>
            
            <!-- 响应式设置 -->
            <div class="bg-red-200 p-4">
                <div class="max-w-7xl 3xl:max-w-9xl 4xl:max-w-10xl 5xl:max-w-11xl mx-auto bg-green-500 text-white p-4 text-center">
                    响应式容器: max-w-7xl (1280px) → 3xl:max-w-9xl (1536px) → 4xl:max-w-10xl (1792px) → 5xl:max-w-11xl (2048px)
                </div>
            </div>
            
            <!-- 屏幕断点显示 -->
            <div class="bg-gray-200 p-4 text-center">
                <div class="hidden 5xl:block bg-purple-500 text-white p-2 mb-2">5xl+ (3840px+) - 4K屏幕</div>
                <div class="hidden 4xl:block 5xl:hidden bg-indigo-500 text-white p-2 mb-2">4xl (2560px - 3839px)</div>
                <div class="hidden 3xl:block 4xl:hidden bg-blue-500 text-white p-2 mb-2">3xl (1920px - 2559px)</div>
                <div class="hidden 2xl:block 3xl:hidden bg-cyan-500 text-white p-2 mb-2">2xl (1536px - 1919px)</div>
                <div class="hidden xl:block 2xl:hidden bg-green-500 text-white p-2 mb-2">xl (1280px - 1535px)</div>
                <div class="hidden lg:block xl:hidden bg-yellow-500 text-white p-2 mb-2">lg (1024px - 1279px)</div>
                <div class="hidden md:block lg:hidden bg-orange-500 text-white p-2 mb-2">md (768px - 1023px)</div>
                <div class="hidden sm:block md:hidden bg-red-500 text-white p-2 mb-2">sm (640px - 767px)</div>
                <div class="block sm:hidden bg-gray-800 text-white p-2 mb-2">xs (&lt; 640px)</div>
            </div>
        </div>
    </div>
    
    <script>
        function updateScreenWidth() {
            document.getElementById('screenWidth').textContent = window.innerWidth;
        }
        
        updateScreenWidth();
        window.addEventListener('resize', updateScreenWidth);
    </script>
</body>
</html>