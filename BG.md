这是多网站项目，目标是构建前端网站，然后在一台服务器上部署多个网站，前端和后端都在同一个目录中，方便开发。
目前只有 2 个网站需要开发，moevend.cn 和 graphock.cn，以后会有更多。

# Moevend 项目

这是一个公司介绍网站，介绍公司主营项目，潮玩商品设计和销售、日用百货零售、软件开发和销售、移动端 app 开发和销售、无人售货机开发和销售。
网站 UI 以商城风格展示主营项目：主页用来展示主营项目和介绍商品，介绍对外服务；关于我们页面，用来展示公司介绍，公司名称杭州虹色萌机科技有限公司，阐述愿景使命和我们丰富的经验。
网站包含导航栏和 footer，copyright 是 2025 年，

# Graphock 项目

这是一个软件产品官网，知识图谱 app，产品名字叫泡泡图谱，主要用来对数据进行图可视化分析，是一个面向银行、保险、证券、期货、软件开发者、科学家、商业分析师、高校、医疗、web3 等多种角色领域的图数据可视化分析软件工具，可以导入多种文件，转化为点边数据进行可视化探索。

# 开发要求

整个项目由 nginx 进行请求的转发，pm2 进行多进程管理，网站全部由 vue3、typescript、vite、tailwindcss、MUI 进行构建开发。
我希望在同一个目录里包含前端和后端，特别注意！！！我希望网站打包后，我上传到服务器，直接就能在解压后的目录中运行脚本来进行网站的部署，网站样式希望好看酷炫，参考 MUI 官网的一些开发者网站风格。
服务端版本是 ubuntu24，node20，已经安装 nginx。
单独写一个 md 文件来描述服务端需要安装的基础软件和初始化配置

# 期望的目录树

moevend-website/
├── sites/ # 所有网站项目
│ ├── moevend/ # 萌贩机主站 (端口 3001)
│ ├── graphock/ # 图数据可视化分析 app (端口 3002)
│ └── example-site/ # 示例项目模板 (端口 3003)
├── nginx/ # nginx 反向代理配置
│ ├── nginx.conf # 主配置文件
│ └── sites-available/ # 各站点配置
├── pm2/ # PM2 进程管理配置
│ └── ecosystem.config.js # PM2 生态系统配置
├── scripts/ # 部署和管理脚本
│ ├── setup-server.sh # 服务器初始化脚本
│ ├── deploy.sh # 部署脚本
│ └── manage-sites.sh # 站点管理脚本
├── DEPLOYMENT.md # 部署指南
└── logs/ # 日志文件目录
