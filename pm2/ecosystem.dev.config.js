module.exports = {
  apps: [
    {
      name: 'moevend-dev',
      script: 'npm',
      args: 'run dev',
      cwd: './sites/moevend',
      instances: 1,
      autorestart: true,
      watch: ['src'],
      ignore_watch: ['node_modules', 'dist'],
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'development',
        PORT: 3001
      },
      log_file: './logs/moevend-dev-combined.log',
      out_file: './logs/moevend-dev-out.log',
      error_file: './logs/moevend-dev-error.log',
      time: true
    },
    {
      name: 'graphock-dev',
      script: 'npm',
      args: 'run dev',
      cwd: './sites/graphock',
      instances: 1,
      autorestart: true,
      watch: ['src'],
      ignore_watch: ['node_modules', 'dist'],
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'development',
        PORT: 3002
      },
      log_file: './logs/graphock-dev-combined.log',
      out_file: './logs/graphock-dev-out.log',
      error_file: './logs/graphock-dev-error.log',
      time: true
    }
  ]
};
