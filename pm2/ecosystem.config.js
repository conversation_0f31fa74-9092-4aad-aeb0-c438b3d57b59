module.exports = {
  apps: [
    {
      name: 'moevend-website',
      script: 'npm',
      args: 'run preview',
      cwd: './sites/moevend',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3001
      },
      log_file: './logs/moevend-combined.log',
      out_file: './logs/moevend-out.log',
      error_file: './logs/moevend-error.log',
      time: true
    },
    {
      name: 'graphock-website',
      script: 'npm',
      args: 'run preview',
      cwd: './sites/graphock',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        PORT: 3002
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3002
      },
      log_file: './logs/graphock-combined.log',
      out_file: './logs/graphock-out.log',
      error_file: './logs/graphock-error.log',
      time: true
    }
  ],

  deploy: {
    production: {
      user: 'deploy',
      host: ['your-server-ip'],
      ref: 'origin/main',
      repo: '**************:your-username/moevend-website.git',
      path: '/var/www/moevend-website',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build:all && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
